---------------------------------------------------------------

[2025-08-04T18:15:46+08:00] ********** POST localhost/api/Account/getBankCardList
[ info ] BaseController action: getbankcardlist
[ info ] BaseController controller: Account
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000966s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000638s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000906s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"lang\",\"token\"]' , '[\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getbankcardlist' , 'Account') [ RunTime:0.000384s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.000795s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `uid` = 1150  AND `status` = 1 ORDER BY `id` DESC [ RunTime:0.000530s ]
---------------------------------------------------------------

[2025-08-04T18:15:46+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.010487s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000811s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000360s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001011s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.001155s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000438s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001065s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000573s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000409s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000265s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000271s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000267s ]
---------------------------------------------------------------

[2025-08-04T18:15:46+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000859s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000539s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000790s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000411s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000762s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000710s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000255s ]
---------------------------------------------------------------

[2025-08-04T18:15:54+08:00] ********** POST localhost/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000755s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000659s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000590s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"DANA\",\"341\",\"50000\",\"111111\",\"\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'draw' , 'Transaction') [ RunTime:0.000443s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000347s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `withdrawals_state` = 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000161s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001050s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000473s ]
---------------------------------------------------------------

[2025-08-04T18:16:00+08:00] ********** POST localhost/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000853s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000640s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000798s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"DANA\",\"341\",\"50000\",\"111111\",\"\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'draw' , 'Transaction') [ RunTime:0.000303s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000178s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `withdrawals_state` = 1 [ RunTime:0.000195s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000169s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000695s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000511s ]
---------------------------------------------------------------

[2025-08-04T18:16:05+08:00] ********** POST localhost/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000972s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000717s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000679s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"DANA\",\"341\",\"50000\",\"111111\",\"\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'draw' , 'Transaction') [ RunTime:0.000298s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000228s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000172s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `withdrawals_state` = 1 [ RunTime:0.000328s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000166s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000753s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000423s ]
---------------------------------------------------------------

[2025-08-04T18:16:11+08:00] ********** GET localhost/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.001471s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001442s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.000590s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000753s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000392s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000768s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000215s ]
---------------------------------------------------------------

[2025-08-04T18:16:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000879s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302578 LIMIT 100 [ RunTime:0.001153s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001034s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-04T18:16:26+08:00] ********** POST localhost/manage/base/setting_edit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000829s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting_edit'  AND `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] UPDATE `ly_setting`  SET `q_server_name` = 'https://www.lotteup.com' , `h_server_name` = '/manage/index' , `manage_title` = 'LOTTEUP' , `admin_title` = 'LOTTEUP' , `manage_ip_white` = 2 , `service_url` = 'https://t.me/LOTTEUP' , `record_number` = 'Copyright© 2015-2024' , `service_hotline` = '400-031-4580' , `official_QQ` = '888888' , `Customer_QQ` = '8888888' , `regment` = '0.00' , `auto_audit` = 1 , `reg_url` = 'https://www.lotteup.com' , `is_sms` = 2 , `reg_code_num` = 0 , `currency` = 'RP' , `is_rec_code` = 1 , `default_language` = 'id' , `sms_user` = 'E10DQW' , `sms_pwd` = 'u2n798' , `robot_level` = 2 , `activity_url` = '' , `web_title` = 'LOTTEUP' , `app_down` = '/app?lang=' , `register_balance` = '10000.00' , `show_credit_interface` = 2 , `reg_init` = 60 , `credit_points_lt` = 30 , `credit_points_task` = 1 , `credit_points_close` = 0 , `signin_push` = 1 , `first_win_push` = 1 , `overdue_ded` = 0 , `dissatisfy_ded` = 0 , `self_first_buy_lottery_times` = 0 , `self_upgrade_lottery_times` = 0 , `self_renew_lottery_times` = 0 , `invite_first_buy_lottery_times` = 1 , `invite_upgrade_lottery_times` = 0 , `invite_renew_lottery_times` = 0 , `withdrawal_fee_switch` = 1 , `withdrawal_bank_fee_rate` = '10.0000' , `weekend_withdrawal_allowed` = 2 , `daily_withdrawal_limit` = 2 , `min_w` = '50000.00' , `max_w` = '********.00' , `withdrawal_time_limit_enabled` = 1 , `withdrawal_start_time` = '10:00' , `withdrawal_end_time` = '23:00' , `info_w` = '1222' , `Mobile_client` = '/upload/resource/mobile_client.png' , `WeChat_official` = '/upload/resource/wechat_official.png' , `seal_img` = '/upload/resource/seal_img.jpg' , `fengge` = 'shopp' , `cn` = 2 , `ft` = 2 , `en` = 1 , `yny` = 1 , `vi` = 2 , `jp` = 2 , `es` = 2 , `ty` = 2 , `yd` = 2 , `ma` = 2 , `pt` = 2  WHERE  `id` = 1 [ RunTime:0.000612s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.000561s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '修改了基本配置' , 1) [ RunTime:0.000360s ]
---------------------------------------------------------------

[2025-08-04T18:16:30+08:00] ********** POST localhost/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.001348s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000686s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000639s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"DANA\",\"341\",\"50000\",\"111111\",\"\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'draw' , 'Transaction') [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000213s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000174s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `withdrawals_state` = 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000603s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000655s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `uid` = 1150  AND `time` >= **********  AND `time` <= **********  AND `state` <> 2 [ RunTime:0.000273s ]
[ sql ] [ SQL ] SELECT `fund_password` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000407s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000605s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000173s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `id` = 341 LIMIT 1 [ RunTime:0.000311s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` - 55000  WHERE  `uid` = 1150  AND `balance` >= 55000 [ RunTime:0.000947s ]
[ sql ] [ SQL ] INSERT INTO `ly_user_withdrawals` (`uid` , `price` , `card_name` , `card_number` , `bank_id` , `bank_name` , `time` , `order_number` , `trade_number` , `fee` , `remarks`) VALUES (1150 , '50000' , 'aa' , '*************' , 19 , 'DANA' , ********** , '202508041816300338274748' , '202508041816300306577375' , 5000 , '尊敬的用户您好！您的编号为202508041816300338274748 的提现处理中，金额￥50000元 服务费：￥5000元，处理时间：2025-08-04 18:16:30') [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `realname`,`username`,`vip_level`,`user_type` FROM `ly_users` WHERE  `id` = 1150 LIMIT 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000571s ]
[ sql ] [ SQL ] INSERT INTO `ly_trade_details` (`types` , `username` , `vip_level` , `user_type` , `uid` , `sid` , `source_uid` , `source_username` , `order_number` , `trade_number` , `trade_time` , `isadmin` , `trade_amount` , `trade_before_balance` , `account_balance` , `account_total_balance` , `remarks` , `remarks_en` , `remarks_id` , `remarks_ft` , `remarks_yd` , `remarks_vi` , `remarks_es` , `remarks_ja` , `remarks_th` , `remarks_ma` , `remarks_pt` , `state` , `trade_type`) VALUES (1 , '222222' , 1 , 2 , 1150 , 1150 , 0 , '' , '202508041816300338274748' , '202508041816300306577375' , ********** , 2 , 55000 , '125000.00' , 70000 , 0 , '平台取款(含手续费￥5000)' , 'Platform withdrawal (including fee $5000)' , 'Penarikan platform (termasuk biaya Rp5000)' , '平台取款(含手續費￥5000)' , 'प्लेटफार्म निकासी (शुल्क ₹5000 सहित)' , 'Rút tiền nền tảng (bao gồm phí 5000đ)' , 'Retiro de plataforma (incluyendo tarifa €5000)' , 'プラットフォーム出金(手数料¥5000を含む)' , 'การถอนเงินแพลตฟอร์ม (รวมค่าธรรมเนียม ฿5000)' , 'Pengeluaran platform (termasuk yuran RM5000)' , 'Retirada da plataforma (incluindo taxa R$5000)' , 3 , 2) [ RunTime:0.000706s ]
---------------------------------------------------------------

[2025-08-04T18:16:30+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000932s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000653s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000598s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000352s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000594s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000350s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.000495s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000867s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000165s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 118 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000203s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000206s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000178s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000237s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000164s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000226s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000212s ]
---------------------------------------------------------------

[2025-08-04T18:16:30+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000786s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000880s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000500s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"lang\",\"token\"]' , '[\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1150' LIMIT 1 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000894s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000671s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= 1754236799  AND `state` = 1 [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000839s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000694s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000757s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1150  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1150  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000937s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1150  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000483s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1149  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000316s ]
---------------------------------------------------------------

[2025-08-04T18:16:30+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_POST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 $_REQUEST[token]: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ info ] 🔍 最终user_token: b0baUnX+IJjMWI/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ
[ sql ] [ DB ] CONNECT:[ UseTime:0.000963s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1150  AND `username` = '222222'  AND `state` = 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000707s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1150 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"id\",\"b0baUnX+IJjMWI\\/dmsIeyohZmNZCBeqb895K6xOcm4HxKKrkgmGdpQ\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000578s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1150 [ RunTime:0.000327s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1150 ORDER BY `add_time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000505s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 117 LIMIT 1 [ RunTime:0.000233s ]
---------------------------------------------------------------

[2025-08-04T18:16:31+08:00] ********** GET localhost/manage/Base/setting
[ sql ] [ DB ] CONNECT:[ UseTime:0.000886s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Base/setting'  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000930s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000764s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000931s ]
[ sql ] [ SQL ] SELECT `grade`,`name` FROM `ly_user_grade` ORDER BY `grade` ASC [ RunTime:0.000347s ]
---------------------------------------------------------------

[2025-08-04T18:16:39+08:00] ********** GET localhost/manage/Bet/userTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000768s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000472s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/usertasklist'  AND `state` = 1 [ RunTime:0.000337s ]
---------------------------------------------------------------

[2025-08-04T18:16:39+08:00] ********** POST localhost/manage/bet/userTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000836s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000510s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/usertasklist'  AND `state` = 1 [ RunTime:0.000339s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000751s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_task`.`id`=`ly_user_task`.`task_id` [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT `ly_task`.`title`,`ly_user_task`.* FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_task`.`id`=`ly_user_task`.`task_id` ORDER BY `trial_time` DESC LIMIT 0,10 [ RunTime:0.000758s ]
---------------------------------------------------------------

[2025-08-04T18:16:39+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000922s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000621s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000654s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000295s ]
---------------------------------------------------------------

[2025-08-04T18:16:51+08:00] ********** GET localhost/manage/bank/controlAudit?id=305
[ sql ] [ DB ] CONNECT:[ UseTime:0.000892s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000663s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000458s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 305 LIMIT 1 [ RunTime:0.000285s ]
---------------------------------------------------------------

[2025-08-04T18:16:54+08:00] ********** POST localhost/manage/bank/controlAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000932s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000676s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508041554533667433733'  AND `state` IN (1,2,3) LIMIT 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `examine` = 1 , `remarks` = '尊敬的用户您好！您的编号为202508041554533667433733 的提现处理中，金额￥50000元 服务费：￥5000元，处理时间：2025-08-04 15:54:53' , `aid` = 24 , `set_time` = **********  WHERE  `order_number` = '202508041554533667433733' [ RunTime:0.000302s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `aid` = 24 , `state` = 4 , `set_time` = ********** , `remarks` = '尊敬的用户您好！您的编号为202508041554533667433733 的提现处理中，金额￥50000元 服务费：￥5000元，处理时间：2025-08-04 15:54:53'  WHERE  `id` = 305 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001050s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 4 , `remarks` = '审核通过，等待支付'  WHERE  `order_number` = '202508041554533667433733' [ RunTime:0.000401s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.000697s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '审核订单号为202508041554533667433733的提现订单。处理状态：审核通过，等待支付' , 1) [ RunTime:0.000295s ]
---------------------------------------------------------------

[2025-08-04T18:16:55+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000877s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000633s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002471s ]
---------------------------------------------------------------

[2025-08-04T18:17:06+08:00] ********** POST localhost/manage/bank/batchWithdrawal
[ sql ] [ DB ] CONNECT:[ UseTime:0.000780s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/batchwithdrawal'  AND `state` = 1 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000504s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6  AND `state` = 1 LIMIT 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 328  AND `state` = 4 LIMIT 1 [ RunTime:0.000384s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 305  AND `state` = 4 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `channel_id` = 6  WHERE  `id` = 305 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `remarks` = '[2025-08-04 18:17:06] JayaPay代付APP代付失败: params : whitelist access denied' , `process_time` = **********  WHERE  `id` = 305 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.001164s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '批量代付失败-订单ID:305（订单号：202508041554533667433733，错误：params : whitelist access denied）' , 0) [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 304  AND `state` = 4 LIMIT 1 [ RunTime:0.001500s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 303  AND `state` = 4 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 302  AND `state` = 4 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 301  AND `state` = 4 LIMIT 1 [ RunTime:0.000211s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 300  AND `state` = 4 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 299  AND `state` = 4 LIMIT 1 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 298  AND `state` = 4 LIMIT 1 [ RunTime:0.000283s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 297  AND `state` = 4 LIMIT 1 [ RunTime:0.000205s ]
[ info ] 开始批量代付: 订单数量=10, 渠道=JayaPay代付APP(jaya_pay), 操作员=admin
[ info ] 批量代付处理订单: ID=305, 订单号=202508041554533667433733, 金额=50000.0000, 渠道=JayaPay代付APP(jaya_pay)
[ info ] TransactionService getBankCodeFromBankId - JayaPay: bank_id=19, DANA -> 10002
[ info ] JayaPay代付银行编码: 10002, bank_id: 19, 卡号: *************
[ info ] JayaPay sign string: 10002IDR20250804181705代付下单user@example.com1S820250727142109000064Transfer08123456789050000aahttps://www.lotteup.com/api/transaction/unifiedWithdrawalCallback*************2025080415545336674337330
[ info ] 批量代付完成: 渠道=JayaPay代付APP(jaya_pay), 成功=0条, 失败=10条, 操作员=admin
[ error ] JayaPay withdrawal direct failed: {"platRespCode":"FAIL","platRespMessage":"params : whitelist access denied"}
[ error ] 批量代付失败: 订单ID=305, 订单号=202508041554533667433733, 错误原因=params : whitelist access denied
---------------------------------------------------------------

[2025-08-04T18:17:06+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000754s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000540s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000443s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000607s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002183s ]
---------------------------------------------------------------

[2025-08-04T18:17:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001028s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000828s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000904s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000260s ]
---------------------------------------------------------------

[2025-08-04T18:18:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000977s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000509s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302704 LIMIT 100 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001068s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000210s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000336s ]
---------------------------------------------------------------

[2025-08-04T18:19:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000949s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302767 LIMIT 100 [ RunTime:0.000480s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001020s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000555s ]
---------------------------------------------------------------

[2025-08-04T18:20:31+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001237s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000830s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302830 LIMIT 100 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001028s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000324s ]
---------------------------------------------------------------

[2025-08-04T18:21:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001348s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001911s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302894 LIMIT 100 [ RunTime:0.000285s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000929s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-04T18:22:34+08:00] ********** GET localhost/manage/index?login=up2025
[ sql ] [ DB ] CONNECT:[ UseTime:0.000913s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000727s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000202s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000650s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000652s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000553s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 2  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000632s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 4  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000427s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 5  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000446s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 3  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 331  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000463s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 347  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000503s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 363  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000422s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000960s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND 1754302954 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000689s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND 1754302954 [ RunTime:0.000721s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000640s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND 1754302954 [ RunTime:0.000226s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000688s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000341s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754302954 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000285s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754302954 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754236799 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754302954 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754236799 [ RunTime:0.000244s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000210s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754302954 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754236799 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000591s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_total` [ RunTime:0.000363s ]
---------------------------------------------------------------

[2025-08-04T18:22:36+08:00] ********** GET localhost/manage/Bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000945s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000681s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000422s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000731s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` ORDER BY `num` ASC [ RunTime:0.000347s ]
---------------------------------------------------------------

[2025-08-04T18:22:37+08:00] ********** POST localhost/manage/bet/taskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.001199s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000581s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bet/tasklist'  AND `state` = 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` [ RunTime:0.000403s ]
[ sql ] [ SQL ] SELECT `ly_task`.*,`ly_task_class`.`group_name` FROM `ly_task` INNER JOIN `ly_task_class` ON `ly_task`.`task_class`=`ly_task_class`.`id` ORDER BY `id` ASC LIMIT 0,10 [ RunTime:0.000384s ]
---------------------------------------------------------------

[2025-08-04T18:22:37+08:00] ********** GET localhost/manage/setting/getFields?fields%5B%5D=auto_audit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001101s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001031s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Setting/getfields'  AND `state` = 1 [ RunTime:0.000635s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001150s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` LIMIT 1 [ RunTime:0.000408s ]
---------------------------------------------------------------

[2025-08-04T18:22:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000963s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000666s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754302958 LIMIT 100 [ RunTime:0.000354s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001006s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000257s ]
---------------------------------------------------------------

[2025-08-04T18:22:39+08:00] ********** GET localhost/manage/Bank/recharge_channel
[ sql ] [ DB ] CONNECT:[ UseTime:0.000955s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000533s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_channel'  AND `state` = 1 [ RunTime:0.000441s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001018s ]
[ sql ] [ SQL ] SELECT * FROM `ly_rechange_type` ORDER BY `type` DESC,`sort` ASC [ RunTime:0.001086s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000854s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 118 [ RunTime:0.000743s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 118 [ RunTime:0.000421s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 117 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 117 [ RunTime:0.000210s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `dispose_time` >= **********  AND `dispose_time` <= **********  AND `state` = 1  AND `type` = 119 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `type` = 119 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SELECT `role_id`,`state` FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `cid` = 3 [ RunTime:0.000370s ]
---------------------------------------------------------------

[2025-08-04T18:22:40+08:00] ********** GET localhost/manage/Bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000717s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000437s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000538s ]
---------------------------------------------------------------

[2025-08-04T18:22:41+08:00] ********** POST localhost/manage/bank/recharge_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000985s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000662s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/recharge_record'  AND `state` = 1 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000692s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000784s ]
[ sql ] [ SQL ] SELECT `ly_user_recharge`.*,`users`.`username`,`rechange_type`.`name`,`rechange_type`.`mode` FROM `ly_user_recharge` INNER JOIN `ly_users` `users` ON `ly_user_recharge`.`uid`=`users`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_user_recharge`.`type`=`rechange_type`.`id` WHERE  `ly_user_recharge`.`type` <> 0  AND `add_time` >= **********  AND `add_time` <= ********** ORDER BY `ly_user_recharge`.`add_time` DESC LIMIT 0,10 [ RunTime:0.002537s ]
---------------------------------------------------------------

[2025-08-04T18:22:41+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000742s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000463s ]
---------------------------------------------------------------

[2025-08-04T18:22:41+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.001250s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000651s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000414s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000958s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.002174s ]
---------------------------------------------------------------

[2025-08-04T18:22:41+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000933s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000813s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000377s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000663s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000372s ]
---------------------------------------------------------------

[2025-08-04T18:22:45+08:00] ********** GET localhost/manage/bank/controlAudit?id=305
[ sql ] [ DB ] CONNECT:[ UseTime:0.000780s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000569s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000401s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 305 LIMIT 1 [ RunTime:0.000359s ]
---------------------------------------------------------------

[2025-08-04T18:22:45+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000841s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000504s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000442s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000674s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000250s ]
---------------------------------------------------------------

[2025-08-04T18:22:51+08:00] ********** POST localhost/manage/bank/executePayment
[ sql ] [ DB ] CONNECT:[ UseTime:0.000851s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000509s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/executepayment'  AND `state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.001774s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508041554533667433733'  AND `state` = 4 LIMIT 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6  AND `state` = 1 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000297s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `aid` = 24 , `state` = 2 , `set_time` = ********** , `remarks` = '代付失败：params : whitelist access denied，资金已退回'  WHERE  `id` = 305 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1150 LIMIT 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` + 55000  WHERE  `uid` = 1150 [ RunTime:0.000609s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000553s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 2 , `remarks` = '代付失败：params : whitelist access denied，资金已退回'  WHERE  `order_number` = '202508041554533667433733' [ RunTime:0.000291s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.000495s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '执行订单号为202508041554533667433733的代付。处理状态：代付失败，资金已退回' , 1) [ RunTime:0.000229s ]
[ info ] TransactionService getBankCodeFromBankId - JayaPay: bank_id=19, DANA -> 10002
[ info ] JayaPay代付银行编码: 10002, bank_id: 19, 卡号: *************
[ info ] JayaPay sign string: 1000220250804182250代付下单user@example.com1S820250727142109000064Transfer08123456789050000aahttps://www.lotteup.com/api/transaction/unifiedWithdrawalCallback*************2025080415545336674337330
[ error ] JayaPay withdrawal direct failed: {"platRespCode":"FAIL","platRespMessage":"params : whitelist access denied"}
---------------------------------------------------------------

[2025-08-04T18:22:57+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000884s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000396s ]
---------------------------------------------------------------

[2025-08-04T18:22:58+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.001063s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000760s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000675s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-04T18:22:58+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000924s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000898s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000576s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name,users.vip_level,uv.name as vip_name,uv.grade as vip_grade,uv.etime as vip_etime,ug.name as grade_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` LEFT JOIN `ly_user_vip` `uv` ON `ly_user_withdrawals`.`uid`=uv.uid AND uv.state = 1 LEFT JOIN `ly_user_grade` `ug` ON `users`.`vip_level`=`ug`.`grade` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.001496s ]
---------------------------------------------------------------

[2025-08-04T18:23:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000937s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000357s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000749s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000184s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000225s ]
---------------------------------------------------------------

[2025-08-04T18:24:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001234s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303084 LIMIT 100 [ RunTime:0.000433s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001037s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000365s ]
---------------------------------------------------------------

[2025-08-04T18:25:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001092s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000881s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303147 LIMIT 100 [ RunTime:0.000544s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000935s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000296s ]
---------------------------------------------------------------

[2025-08-04T18:26:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001225s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000488s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303210 LIMIT 100 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000975s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000494s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000302s ]
---------------------------------------------------------------

[2025-08-04T18:27:56+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000806s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000778s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303275 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000763s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000243s ]
---------------------------------------------------------------

[2025-08-04T18:28:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002857s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000895s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303339 LIMIT 100 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001623s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000589s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000480s ]
---------------------------------------------------------------

[2025-08-04T18:30:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000936s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000784s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303402 LIMIT 100 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000944s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000306s ]
---------------------------------------------------------------

[2025-08-04T18:31:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001061s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000514s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754303466 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000863s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= ********** LIMIT 600 [ RunTime:0.000335s ]
