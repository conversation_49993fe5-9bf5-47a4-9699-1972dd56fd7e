<?php /*a:1:{s:64:"/var/www/html/application/manage/view/bet/financial_dateils.html";i:1609314392;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>流水详情</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <table class="layui-table" lay-even lay-skin="nob" lay-skin="row">
                            <tbody>
                                <tr>
                                    <td>订单号</td>
                                    <td><strong><?php echo htmlentities($info['order_number']); ?></strong></td>
                                    <td>流水号</td>
                                    <td><strong><?php echo htmlentities($info['trade_number']); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>用户</td>
                                    <td><strong><?php echo isset($info['mName']) ? htmlentities($info['mName']) : ''; ?></strong></td>
                                    <td>目标</td>
                                    <td><strong><?php echo isset($info['uName']) ? htmlentities($info['uName']) : ''; ?></strong></td>
                                </tr>
                                <tr>
                                    <td>交易类型</td>
                                    <td><strong><?php echo htmlentities($info['tradeType']); ?></strong></td>
                                    <td>交易金额</td>
                                    <td><strong><?php echo htmlentities($info['trade_amount']); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>交易前金额</td>
                                    <td><strong><?php echo htmlentities($info['trade_before_balance']); ?></strong></td>
                                    <td>交易后金额</td>
                                    <td><strong><?php echo htmlentities($info['account_balance']); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>时间</td>
                                    <td><strong><?php echo htmlentities(date("Y-m-d H:i:s",!is_numeric(match_msecdate($info['trade_time']))? strtotime(match_msecdate($info['trade_time'])) : match_msecdate($info['trade_time']))); ?></strong></td>
                                    <td>备注</td>
                                    <td><strong><?php echo htmlentities($info['remarks']); ?></strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>
</body>
</html>