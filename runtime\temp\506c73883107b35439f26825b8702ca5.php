<?php /*a:1:{s:62:"/var/www/html/application/manage/view/bet/task_model_list.html";i:1752040628;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务模板列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card" style="padding: 10px;">
                    <form class="layui-form search">
                        <div class="layui-form-item">
                        <div class="layui-inline">
                                <label class="layui-form-label">模板名</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="username" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">任务标题</label>
                                <div class="layui-input-inline">
                                    <input class="layui-input" name="title" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-inline" style="text-align: center;">
                                <button type="button" class="layui-btn" data-type="search">搜索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <table class="layui-hide" id="taskModelList" lay-filter="taskModelList"></table>
                </div>
            </div>
        </div>
    </div>
    <!-- 头部左侧工具栏 -->
    <script type="text/html" id="toolbarDemo">
        <div class="layui-btn-container layui-btn-group">
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-event="add">
                <i class="layui-icon">&#xe654;</i>
            </button>
        </div>
    </script>
    <!-- 表格右侧操作单元 -->
    <script type="text/html" id="action">
        <div class="layui-btn-group">
            <button type="button" class="layui-btn layui-btn-xs" lay-event="edit">
                <i class="layui-icon">&#xe642;</i>
            </button>
            <button type="button" class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">
                <i class="layui-icon">&#xe640;</i>
            </button>
        </div>
    </script>
    <!-- 表单元素 -->

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bet.js"></script>
<script>
    layui.use(['layer', 'table'], function(){
        var $ = layui.$
        ,layer = layui.layer
        ,table = layui.table;

        //方法级渲染
        table.render({
            elem: '#taskModelList'
            ,title: '任务列表'
            ,url: '/manage/bet/taskModelList'
            ,method: 'post'
            ,cols: [[
                {checkbox: true, fixed: true, totalRowText: '合计'}
                ,{field: 'id', title: '编号', sort: true, fixed: 'left'}
				,{field: 'group_name', title: '任务分类', sort: true, fixed: 'left'}
				,{field: 'name', title: '模板名', sort: true, fixed: 'left'}
                ,{field: 'title', title: '任务标题', sort: true, fixed: 'left'}
                ,{field: 'main_image', title: '主图', width: 100, templet: function(d){
                    if(d.main_image && d.main_image !== '') {
                        return '<img src="' + d.main_image + '" style="max-width: 60px; max-height: 60px; cursor: pointer;" onclick="layer.photos({photos: {data: [{src: \'' + d.main_image + '\'}]}, anim: 5});">';
                    } else {
                        return '<span style="color: #999;">无图片</span>';
                    }
                }}
                ,{title: '管理操作', width: '20%', toolbar: '#action'}
            ]]
            ,cellMinWidth: 100
            ,toolbar: '#toolbarDemo'
            ,defaultToolbar: ['filter', 'print', 'exports']
            ,totalRow: true
            ,page: {
                layout: ['count', 'prev', 'page', 'next', 'limit', 'refresh', 'skip']
            }
            ,skin: 'row' //行边框风格
            ,even: true //开启隔行背景
        });

        //监听排序事件
        table.on('sort(taskModelList)', function(obj){ //注：sort 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
            //尽管我们的 table 自带排序功能，但并没有请求服务端。
            //有些时候，你可能需要根据当前排序的字段，重新向服务端发送请求，从而实现服务端排序，如：
            table.reload('taskModelList', {
                initSort: obj //记录初始排序，如果不设的话，将无法标记表头的排序状态。
                ,where: { //请求参数（注意：这里面的参数可任意定义，并非下面固定的格式）
                    sortField: obj.field //排序字段
                    ,sortType: obj.type //排序方式
                }
            });
        });
        //监听行双击事件
        table.on('rowDouble(taskModelList)', function(obj){

        });

        active = {
            search: function(){
                //执行重载
                table.reload('taskModelList', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    ,where: {
						username: $("input[name='name']").val()
                        ,title: $("input[name='title']").val()
                    }
                }, 'data');
            }
        };
        $('.search .layui-btn').on('click', function(){
            var type = $(this).data('type');
            active[type] ? active[type].call(this) : '';
        });
    });
</script>
</body>
</html>