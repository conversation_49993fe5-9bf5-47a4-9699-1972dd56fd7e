<?php
/**
 * JayaPay签名测试脚本
 * 用于验证签名生成逻辑是否与官方文档一致
 */

class JayaPaySignatureTest
{
    /**
     * 构建JayaPay签名字符串 - 按照官方规则
     * @param array $params 参数数组
     * @return string 待签名字符串
     */
    public static function buildSignString($params)
    {
        // 移除签名参数
        unset($params['sign']);
        unset($params['platSign']);
        
        // 过滤空值
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });
        
        // 按Key的ASCII码排序
        ksort($params);
        
        // 只取参数值进行拼接
        $signString = '';
        foreach ($params as $key => $value) {
            $signString .= $value;
        }
        
        return $signString;
    }
    
    /**
     * 测试官方文档示例参数
     */
    public static function testOfficialExample()
    {
        echo "=== JayaPay官方文档示例测试 ===\n\n";
        
        // 官方文档示例参数
        $officialParams = [
            'merchantCode' => 'S820211021094748000001',
            'method' => 'BCA',
            'orderNum' => 'T1642592278863',
            'payMoney' => '10000',
            'productDetail' => 'Test Pay',
            'notifyUrl' => 'your notify url',
            'dateTime' => '20221202125813',
            'name' => 'JackMa',
            'expiryPeriod' => '1440',
            'email' => '<EMAIL>',
            'phone' => '082122965511'
        ];
        
        // 官方给出的拼接字符串
        $officialSignString = '2022-01-01 10:55:00test@email.com1440S820211021094748000001014JackMayour notify urlT1642593166888150.60082122965511Test Pay';
        
        echo "1. 官方示例参数:\n";
        foreach ($officialParams as $key => $value) {
            echo "   {$key} = {$value}\n";
        }
        
        echo "\n2. 按ASCII排序后的参数顺序:\n";
        $sortedKeys = array_keys($officialParams);
        sort($sortedKeys);
        foreach ($sortedKeys as $key) {
            echo "   {$key} = {$officialParams[$key]}\n";
        }
        
        // 生成我们的拼接字符串
        $ourSignString = self::buildSignString($officialParams);
        
        echo "\n3. 拼接字符串对比:\n";
        echo "   官方拼接字符串: {$officialSignString}\n";
        echo "   我们生成的字符串: {$ourSignString}\n";
        
        echo "\n4. 对比结果:\n";
        if ($ourSignString === $officialSignString) {
            echo "   ✅ 完全一致！签名逻辑正确\n";
        } else {
            echo "   ❌ 不一致，存在差异\n";
            echo "   差异分析:\n";
            self::analyzeStringDifference($officialSignString, $ourSignString);
        }
        
        return $ourSignString === $officialSignString;
    }
    
    /**
     * 测试我们项目中的实际参数
     */
    public static function testProjectParams()
    {
        echo "\n\n=== 项目实际参数测试 ===\n\n";
        
        // 从日志中提取的实际参数
        $projectParams = [
            'bankCode' => '10002',
            'dateTime' => '**************',
            'description' => '代付下单',
            'email' => '<EMAIL>',
            'feeType' => '1',
            'merchantCode' => 'S820250727142109000064',
            'method' => 'Transfer',
            'mobile' => '************',
            'money' => '50000',
            'name' => 'aa',
            'notifyUrl' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback',
            'number' => '*************',
            'orderNum' => '202508041554533667433733',
            'orderType' => '0'
        ];
        
        // 从日志中的实际拼接字符串
        $actualSignString = '10002**************代付下单user@example.com1S820250727142109000064Transfer************50000aahttps://www.lotteup.com/api/transaction/unifiedWithdrawalCallback*************2025080415545336674337330';
        
        echo "1. 项目实际参数:\n";
        foreach ($projectParams as $key => $value) {
            echo "   {$key} = {$value}\n";
        }
        
        echo "\n2. 按ASCII排序后的参数顺序:\n";
        $sortedKeys = array_keys($projectParams);
        sort($sortedKeys);
        foreach ($sortedKeys as $key) {
            echo "   {$key} = {$projectParams[$key]}\n";
        }
        
        // 生成我们的拼接字符串
        $ourSignString = self::buildSignString($projectParams);
        
        echo "\n3. 拼接字符串对比:\n";
        echo "   日志中的字符串: {$actualSignString}\n";
        echo "   我们生成的字符串: {$ourSignString}\n";
        
        echo "\n4. 对比结果:\n";
        if ($ourSignString === $actualSignString) {
            echo "   ✅ 完全一致！项目签名逻辑正确\n";
        } else {
            echo "   ❌ 不一致，存在差异\n";
            echo "   差异分析:\n";
            self::analyzeStringDifference($actualSignString, $ourSignString);
        }
        
        return $ourSignString === $actualSignString;
    }
    
    /**
     * 分析字符串差异
     */
    public static function analyzeStringDifference($expected, $actual)
    {
        $expectedLen = strlen($expected);
        $actualLen = strlen($actual);
        
        echo "   预期长度: {$expectedLen}, 实际长度: {$actualLen}\n";
        
        if ($expectedLen !== $actualLen) {
            echo "   长度不一致！\n";
        }
        
        // 找出第一个不同的字符位置
        $minLen = min($expectedLen, $actualLen);
        for ($i = 0; $i < $minLen; $i++) {
            if ($expected[$i] !== $actual[$i]) {
                echo "   第一个差异位置: {$i}\n";
                echo "   预期字符: '{$expected[$i]}' (ASCII: " . ord($expected[$i]) . ")\n";
                echo "   实际字符: '{$actual[$i]}' (ASCII: " . ord($actual[$i]) . ")\n";
                
                // 显示差异位置前后的上下文
                $start = max(0, $i - 10);
                $end = min($minLen, $i + 10);
                echo "   上下文预期: '" . substr($expected, $start, $end - $start) . "'\n";
                echo "   上下文实际: '" . substr($actual, $start, $end - $start) . "'\n";
                break;
            }
        }
    }
}

// 运行测试
echo "JayaPay签名生成测试\n";
echo "==================\n";

$test1 = JayaPaySignatureTest::testOfficialExample();
$test2 = JayaPaySignatureTest::testProjectParams();

echo "\n\n=== 测试总结 ===\n";
echo "官方示例测试: " . ($test1 ? "✅ 通过" : "❌ 失败") . "\n";
echo "项目参数测试: " . ($test2 ? "✅ 通过" : "❌ 失败") . "\n";

if ($test1 && $test2) {
    echo "\n🎉 所有测试通过！签名生成逻辑完全正确！\n";
} else {
    echo "\n⚠️  存在问题，需要进一步检查签名生成逻辑\n";
}
?>
