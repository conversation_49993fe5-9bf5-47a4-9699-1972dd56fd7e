<?php
/**
 * JayaPay签名测试脚本
 * 使用项目中JayaPayService的buildSignString方法逻辑
 */

class JayaPaySignatureTest
{
    /**
     * 构建JayaPay签名字符串 - 直接复制自项目中的JayaPayService::buildSignString方法
     * @param array $params 参数数组
     * @return string 待签名字符串
     */
    public static function buildSignString($params)
    {
        // 移除签名参数（发送请求时移除sign，验证回调时移除platSign）
        unset($params['sign']);
        unset($params['platSign']);

        // 过滤空值
        $params = array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        });

        // 按Key的ASCII码排序
        ksort($params);

        // 只取参数值进行拼接
        $signString = '';
        foreach ($params as $value) {
            $signString .= $value;
        }

        // 模拟项目中的日志输出
        echo "   [DEBUG] JayaPay sign string: {$signString}\n";

        return $signString;
    }
    
    /**
     * 测试官方文档示例参数 - 使用拼接字符串中的实际值
     */
    public static function testOfficialExample()
    {
        echo "=== JayaPay官方文档示例测试（使用拼接字符串中的实际值） ===\n\n";

        // 根据官方拼接字符串反推出的实际参数值
        // 拼接字符串: 2022-01-01 10:55:00test@email.com1440S820211021094748000001014JackMayour notify urlT1642593166888150.60082122965511Test Pay
        $officialParams = [
            'dateTime' => '2022-01-01 10:55:00',        // 第1个值
            'email' => '<EMAIL>',                // 第2个值
            'expiryPeriod' => '1440',                   // 第3个值
            'merchantCode' => 'S820211021094748000001', // 第4个值
            'method' => '014',                          // 第5个值（不是BCA，而是014）
            'name' => 'JackMa',                         // 第6个值
            'notifyUrl' => 'your notify url',           // 第7个值
            'orderNum' => 'T1642593166888',             // 第8个值（不是T1642592278863）
            'payMoney' => '150.60',                     // 第9个值（不是10000）
            'phone' => '082122965511',                  // 第10个值
            'productDetail' => 'Test Pay'               // 第11个值
        ];

        // 官方给出的拼接字符串
        $officialSignString = '2022-01-01 10:55:00test@email.com1440S820211021094748000001014JackMayour notify urlT1642593166888150.60082122965511Test Pay';
        
        echo "1. 从拼接字符串反推的实际参数:\n";
        foreach ($officialParams as $key => $value) {
            echo "   {$key} = {$value}\n";
        }

        echo "\n2. 按ASCII排序后的参数顺序:\n";
        $sortedKeys = array_keys($officialParams);
        sort($sortedKeys);
        foreach ($sortedKeys as $key) {
            echo "   {$key} = {$officialParams[$key]}\n";
        }

        // 生成我们的拼接字符串
        $ourSignString = self::buildSignString($officialParams);

        echo "\n3. 拼接字符串对比:\n";
        echo "   官方拼接字符串: {$officialSignString}\n";
        echo "   我们生成的字符串: {$ourSignString}\n";

        echo "\n4. 对比结果:\n";
        if ($ourSignString === $officialSignString) {
            echo "   ✅ 完全一致！签名逻辑正确\n";
            echo "   这证明了我们的签名生成算法与JayaPay官方完全一致\n";
        } else {
            echo "   ❌ 不一致，存在差异\n";
            echo "   差异分析:\n";
            self::analyzeStringDifference($officialSignString, $ourSignString);
        }
        
        return $ourSignString === $officialSignString;
    }
    
    /**
     * 测试WatchPay签名逻辑
     */
    public static function testWatchPaySignature()
    {
        echo "\n\n=== WatchPay签名测试 ===\n\n";

        // WatchPay官方示例参数（从加密规则文档）
        $watchPayParams = [
            'goods_name' => 'test',
            'mch_id' => '123456789',
            'mch_order_no' => '2021-04-13 17:32:28',
            'notify_url' => 'http://www.baidu.com/notify_url.jsp',
            'order_date' => '2021-04-13 17:32:25',
            'pay_type' => '122',
            'trade_amount' => '100'
        ];

        $secretKey = '0936D7E86164C2D53C8FF8AD06ED6D09';
        $expectedSignString = 'goods_name=test&mch_id=123456789&mch_order_no=2021-04-13 17:32:28&notify_url=http://www.baidu.com/notify_url.jsp&order_date=2021-04-13 17:32:25&pay_type=122&trade_amount=100&key=0936D7E86164C2D53C8FF8AD06ED6D09';
        $expectedSign = '36e9ff3420fafb6fa3f20e7067c3148c';

        echo "1. WatchPay官方示例参数:\n";
        foreach ($watchPayParams as $key => $value) {
            echo "   {$key} = {$value}\n";
        }
        echo "   key = {$secretKey}\n";

        // 生成我们的签名字符串
        $ourSignString = self::buildWatchPaySignString($watchPayParams, $secretKey);
        $ourSign = strtolower(md5($ourSignString));

        echo "\n2. 签名字符串对比:\n";
        echo "   官方签名字符串: {$expectedSignString}\n";
        echo "   我们生成的字符串: {$ourSignString}\n";

        echo "\n3. MD5签名对比:\n";
        echo "   官方MD5签名: {$expectedSign}\n";
        echo "   我们生成的签名: {$ourSign}\n";

        echo "\n4. 对比结果:\n";
        $stringMatch = $ourSignString === $expectedSignString;
        $signMatch = $ourSign === $expectedSign;

        if ($stringMatch && $signMatch) {
            echo "   ✅ 完全一致！WatchPay签名逻辑正确\n";
        } else {
            echo "   ❌ 存在差异\n";
            if (!$stringMatch) {
                echo "   签名字符串不一致\n";
                self::analyzeStringDifference($expectedSignString, $ourSignString);
            }
            if (!$signMatch) {
                echo "   MD5签名不一致\n";
            }
        }

        return $stringMatch && $signMatch;
    }

    /**
     * 构建WatchPay签名字符串 - 按照官方规则
     */
    public static function buildWatchPaySignString($params, $secretKey)
    {
        // 移除签名字段
        unset($params['sign']);
        unset($params['sign_type']);
        unset($params['signType']);

        // 移除空值参数
        $filteredParams = [];
        foreach ($params as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $filteredParams[$key] = $value;
            }
        }

        // 按键名ASCII码排序
        ksort($filteredParams);

        // 构建签名字符串 - k=v&k=v格式
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 添加密钥
        $signStr .= 'key=' . $secretKey;

        return $signStr;
    }

    /**
     * 测试我们项目中的实际参数
     */
    public static function testProjectParams()
    {
        echo "\n\n=== JayaPay项目实际参数测试 ===\n\n";
        
        // 从日志中提取的实际参数
        $projectParams = [
            'bankCode' => '10002',
            'dateTime' => '**************',
            'description' => '代付下单',
            'email' => '<EMAIL>',
            'feeType' => '1',
            'merchantCode' => 'S820250727142109000064',
            'method' => 'Transfer',
            'mobile' => '************',
            'money' => '50000',
            'name' => 'aa',
            'notifyUrl' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback',
            'number' => '*************',
            'orderNum' => '202508041554533667433733',
            'orderType' => '0'
        ];
        
        // 从日志中的实际拼接字符串
        $actualSignString = '10002**************代付下单user@example.com1S820250727142109000064Transfer************50000aahttps://www.lotteup.com/api/transaction/unifiedWithdrawalCallback*************2025080415545336674337330';
        
        echo "1. 项目实际参数:\n";
        foreach ($projectParams as $key => $value) {
            echo "   {$key} = {$value}\n";
        }
        
        echo "\n2. 按ASCII排序后的参数顺序:\n";
        $sortedKeys = array_keys($projectParams);
        sort($sortedKeys);
        foreach ($sortedKeys as $key) {
            echo "   {$key} = {$projectParams[$key]}\n";
        }
        
        // 生成我们的拼接字符串
        $ourSignString = self::buildSignString($projectParams);
        
        echo "\n3. 拼接字符串对比:\n";
        echo "   日志中的字符串: {$actualSignString}\n";
        echo "   我们生成的字符串: {$ourSignString}\n";
        
        echo "\n4. 对比结果:\n";
        if ($ourSignString === $actualSignString) {
            echo "   ✅ 完全一致！项目签名逻辑正确\n";
        } else {
            echo "   ❌ 不一致，存在差异\n";
            echo "   差异分析:\n";
            self::analyzeStringDifference($actualSignString, $ourSignString);
        }
        
        return $ourSignString === $actualSignString;
    }
    
    /**
     * 分析字符串差异
     */
    public static function analyzeStringDifference($expected, $actual)
    {
        $expectedLen = strlen($expected);
        $actualLen = strlen($actual);
        
        echo "   预期长度: {$expectedLen}, 实际长度: {$actualLen}\n";
        
        if ($expectedLen !== $actualLen) {
            echo "   长度不一致！\n";
        }
        
        // 找出第一个不同的字符位置
        $minLen = min($expectedLen, $actualLen);
        for ($i = 0; $i < $minLen; $i++) {
            if ($expected[$i] !== $actual[$i]) {
                echo "   第一个差异位置: {$i}\n";
                echo "   预期字符: '{$expected[$i]}' (ASCII: " . ord($expected[$i]) . ")\n";
                echo "   实际字符: '{$actual[$i]}' (ASCII: " . ord($actual[$i]) . ")\n";
                
                // 显示差异位置前后的上下文
                $start = max(0, $i - 10);
                $end = min($minLen, $i + 10);
                echo "   上下文预期: '" . substr($expected, $start, $end - $start) . "'\n";
                echo "   上下文实际: '" . substr($actual, $start, $end - $start) . "'\n";
                break;
            }
        }
    }
}

// 运行测试
echo "JayaPay & WatchPay签名生成测试\n";
echo "==============================\n";

$test1 = JayaPaySignatureTest::testOfficialExample();
$test2 = JayaPaySignatureTest::testWatchPaySignature();
$test3 = JayaPaySignatureTest::testProjectParams();

echo "\n\n=== 测试总结 ===\n";
echo "JayaPay官方示例测试: " . ($test1 ? "✅ 通过" : "❌ 失败") . "\n";
echo "WatchPay官方示例测试: " . ($test2 ? "✅ 通过" : "❌ 失败") . "\n";
echo "JayaPay项目参数测试: " . ($test3 ? "✅ 通过" : "❌ 失败") . "\n";

if ($test1 && $test2 && $test3) {
    echo "\n🎉 所有测试通过！JayaPay和WatchPay签名生成逻辑完全正确！\n";
} else {
    echo "\n⚠️  存在问题，需要进一步检查签名生成逻辑\n";
}
?>
