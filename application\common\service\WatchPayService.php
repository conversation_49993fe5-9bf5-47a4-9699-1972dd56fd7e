<?php
namespace app\common\service;

class WatchPayService
{
    /**
     * 获取WatchPay配置
     */
    public function getConfig()
    {
        try {
            // 直接读取配置文件，修复路径问题
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                $watchPayConfig = $paymentConfig['watch_pay'] ?? null;

                if ($watchPayConfig && $watchPayConfig['enabled']) {
                    \think\facade\Log::info('WatchPay config loaded from file: ' . json_encode($watchPayConfig));
                    return $watchPayConfig;
                }
            }

            \think\facade\Log::info('WatchPay config not found or disabled');
            return null;

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay config error: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 验证配置是否完整
     */
    public function validateConfig()
    {
        $config = $this->getConfig();
        if (!$config) {
            return false;
        }

        // 检查是否有至少一个启用的国家配置
        $countries = $config['countries'] ?? [];
        foreach ($countries as $countryConfig) {
            if ($countryConfig['enabled'] && !empty($countryConfig['merchant_id']) && !empty($countryConfig['pay_key'])) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取支持的国家列表
     */
    public function getCountries($lang = 'id')
    {
        $texts = $this->getTexts($lang);

        try {
            // 从充值渠道配置中获取国家列表
            $watchPayConfig = $this->getConfig();

            if (!$watchPayConfig) {
                return [
                    'code' => 0,
                    'msg' => $texts['messages']['get_countries_failed']
                ];
            }

            $countries = [];
            if (isset($watchPayConfig['countries'])) {
                foreach ($watchPayConfig['countries'] as $countryCode => $countryConfig) {
                    $payTypes = [];
                    if (isset($countryConfig['pay_types'])) {
                        foreach ($countryConfig['pay_types'] as $typeCode => $typeConfig) {
                            if ($typeConfig['enabled']) {
                                $payTypes[] = [
                                    'code' => $typeCode,
                                    'name' => $typeConfig['name'],
                                    'type' => $this->getPayTypeCategory($typeCode)
                                ];
                            }
                        }
                    }

                    $countries[] = [
                        'code' => $countryCode,
                        'name' => $texts['countries'][$countryCode] ?? $countryConfig['name'],
                        'name_en' => $countryConfig['name'],
                        'flag' => $this->getCountryFlag($countryCode),
                        'currency' => $countryConfig['currency'],
                        'min_amount' => $countryConfig['min_amount'],
                        'max_amount' => $countryConfig['max_amount'],
                        'fee_rate' => 2.0, // 可以从配置中读取
                        'pay_types' => $payTypes
                    ];
                }
            }

            // 如果没有配置，返回默认配置
            if (empty($countries)) {
                $countries = [
                    [
                        'code' => 'ID',
                        'name' => $texts['countries']['ID'],
                        'name_en' => 'Indonesia',
                        'flag' => '🇮🇩',
                        'currency' => 'IDR',
                        'min_amount' => 50000,
                        'max_amount' => ********,
                        'fee_rate' => 2.0,
                        'pay_types' => [
                            ['code' => '220', 'name' => $texts['pay_types']['online_banking_b2c_class1'], 'type' => 'online'],
                            ['code' => '223', 'name' => $texts['pay_types']['qris_scan_class2'], 'type' => 'scan']
                        ]
                    ]
                ];
            }

            return [
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => $countries
            ];

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $texts['messages']['get_countries_failed'] . ': ' . $e->getMessage()
            ];
        }
    }

    /**
     * 根据渠道ID获取支付方式
     */
    public function getPayTypes($rechargeId, $lang = 'id')
    {
        $texts = $this->getTexts($lang);

        // 记录调试信息
        \think\facade\Log::info('getPayTypes called with recharge_id: ' . $rechargeId);

        try {
            if (empty($rechargeId)) {
                return ['code' => 0, 'msg' => '渠道ID不能为空'];
            }

            // 根据渠道ID获取配置
            $rechargeType = model('RechangeType')->where(['id' => $rechargeId, 'mode' => 'watchPay', 'state' => 1])->find();

            if (!$rechargeType) {
                return ['code' => 0, 'msg' => '渠道不存在'];
            }

            // 获取渠道的原始手续费
            $channelFee = $rechargeType['fee'] ?? 0;

            // 从配置文件获取配置
            try {
                $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
                if (!file_exists($configPath)) {
                    $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
                }

                if (!file_exists($configPath)) {
                    return ['code' => 0, 'msg' => '支付配置文件不存在'];
                }

                $config = include $configPath;
                if (!isset($config['global_pay']['countries'])) {
                    return ['code' => 0, 'msg' => 'GlobalPay配置格式错误'];
                }
            } catch (\Exception $e) {
                return ['code' => 0, 'msg' => '读取配置文件失败: ' . $e->getMessage()];
            }

            // 获取第一个国家的配置（目前主要支持印尼）
            $countryConfig = reset($config['global_pay']['countries']);
            $countryCode = key($config['global_pay']['countries']);

            $payTypes = [];
            if (isset($countryConfig['pay_types'])) {
                foreach ($countryConfig['pay_types'] as $typeCode => $typeConfig) {
                    if ($typeConfig['enabled']) {
                        // 获取多语言名称
                        $localizedName = $this->getLocalizedPayTypeName($typeCode, $texts);

                        $payTypes[] = [
                            'code' => $typeCode,
                            'name' => $localizedName ?: $typeConfig['name'], // 如果没有多语言版本，使用原名称
                            'type' => $this->getPayTypeCategory($typeCode),
                            'requires_bank_code' => $typeConfig['requires_bank_code'] ?? false // 添加是否需要bank_code字段
                        ];
                    }
                }
            }

            // 如果没有可用的支付类型
            if (empty($payTypes)) {
                return ['code' => 0, 'msg' => '该渠道暂无可用的支付方式'];
            }

            return [
                'code' => 1,
                'msg' => $texts['messages']['success'],
                'data' => [
                    'recharge_id' => $rechargeId,
                    'country_code' => $countryCode,
                    'country_name' => $texts['countries'][$countryCode] ?? $countryConfig['name'],
                    'currency' => $countryConfig['currency'],
                    'min_amount' => $countryConfig['min_amount'],
                    'max_amount' => $countryConfig['max_amount'],
                    'fee_rate' => $channelFee,
                    'pay_types' => $payTypes
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => $texts['messages']['get_pay_types_failed'] . ': ' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建充值订单
     */
    public function createRechargeOrder($params)
    {
        $lang = $params['lang'] ?? 'id';
        $texts = $this->getTexts($lang);

        // 记录调试信息
        \think\facade\Log::info('WatchPay createRechargeOrder called with param: ' . json_encode($params));
        \think\facade\Log::info('WatchPay createRechargeOrder lang: ' . $lang);

        try {
            // 检查 $params 是否为数组
            if (!is_array($params)) {
                \think\facade\Log::error('WatchPay createRechargeOrder param is not array: ' . var_export($params, true));
                return ['code' => 0, 'msg' => '参数格式错误'];
            }

            // 参数验证
            $required = ['token', 'recharge_id', 'pay_type', 'amount'];
            foreach ($required as $field) {
                if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                    return ['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"];
                }
            }

            // 验证用户token
            $token = $params['token'];
            $userArr = explode(',', auth_code($token, 'DECODE'));
            $uid = $userArr[0] ?? 0;
            $username = $userArr[1] ?? '';

            if (empty($uid)) {
                return ['code' => 0, 'msg' => $texts['messages']['invalid_login']];
            }

            // 验证渠道ID并获取渠道信息
            $rechargeType = model('RechangeType')->where('id', $params['recharge_id'])->where('state', 1)->find();
            if (!$rechargeType) {
                return ['code' => 0, 'msg' => '充值渠道不存在或已禁用'];
            }

            if ($rechargeType['mode'] !== 'watchPay') {
                return ['code' => 0, 'msg' => '渠道类型错误，非WatchPay支付渠道'];
            }

            // 生成订单号
            $orderNo = 'GP' . date('YmdHis') . rand(1000, 9999);

            // 创建充值记录
            $rechargeData = [
                'uid' => $uid,
                'order_number' => $orderNo,
                'type' => $params['recharge_id'], // 使用传入的渠道ID
                'money' => $params['amount'],
                'daozhang_money' => $params['amount'],
                'state' => 3, // 待支付
                'add_time' => time(),
                'postscript' => $rechargeType['name'],
                'remarks' => json_encode([
                    'pay_type' => $params['pay_type'],
                    'pay_method' => 'watchPay'
                ])
            ];

            $rechargeId = model('UserRecharge')->insertGetId($rechargeData);
            
            if (!$rechargeId) {
                return ['code' => 0, 'msg' => '订单创建失败'];
            }

            // 构建WatchPay充值订单数据
            $orderData = [
                'recharge_id' => $params['recharge_id'],
                'pay_type' => $params['pay_type'],
                'order_no' => $orderNo,
                'amount' => $params['amount'],
                'notify_url' => $this->getNotifyUrl($params['recharge_id'])
            ];

            // 如果前端传递了bank_code，添加到订单数据中
            if (!empty($params['bank_code'])) {
                $orderData['bank_code'] = $params['bank_code'];
                \think\facade\Log::info('WatchPay received bank_code: ' . $params['bank_code']);
            }

            // 直接调用WatchPay充值API
            $result = $this->createWatchPayRechargeOrder($orderData);

            // 记录调试信息
            \think\facade\Log::info('WatchPay createOrder result: ' . json_encode($result));

            // 检查返回结果类型
            if (!is_array($result)) {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();
                \think\facade\Log::error('WatchPay returned non-array result: ' . var_export($result, true));
                return [
                    'code' => 0,
                    'msg' => '第三方支付服务返回格式错误'
                ];
            }

            if (isset($result['code']) && $result['code'] == 1) {
                // 检查是否有支付URL
                if (!isset($result['data']) || !is_array($result['data']) || !isset($result['data']['pay_url'])) {
                    // 删除失败的订单
                    model('UserRecharge')->where('id', $rechargeId)->delete();
                    return [
                        'code' => 0,
                        'msg' => '第三方支付服务未返回支付链接'
                    ];
                }

                // 更新充值记录
                model('UserRecharge')->where('id', $rechargeId)->update([
                    'submitUrl' => $result['data']['pay_url']
                ]);

                return [
                    'code' => 1,
                    'msg' => '订单创建成功',
                    'data' => [
                        'order_no' => $orderNo,
                        'pay_url' => $result['data']['pay_url'],
                        'amount' => $params['amount'],
                        'recharge_id' => $rechargeId
                    ]
                ];
            } else {
                // 删除失败的订单
                model('UserRecharge')->where('id', $rechargeId)->delete();

                // 返回错误信息
                $errorMsg = isset($result['msg']) ? $result['msg'] : '支付订单创建失败';
                return [
                    'code' => 0,
                    'msg' => $errorMsg
                ];
            }

        } catch (\Exception $e) {
            return [
                'code' => 0,
                'msg' => '创建订单失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 创建提现订单
     */
    public function createWithdrawal($params)
    {
        $lang = $params['lang'] ?? 'id';
        $texts = $this->getTexts($lang);

        try {
            // 参数验证
            $required = ['token', 'withdrawal_id', 'amount', 'bank_code', 'account_number', 'account_name'];
            foreach ($required as $field) {
                if (!isset($params[$field]) || empty($params[$field])) {
                    return ['code' => 0, 'msg' => $texts['messages']['param_required'] . ": {$field}"];
                }
            }

            // 验证用户token
            $userArr = explode(',', auth_code($params['token'], 'DECODE'));
            $uid = $userArr[0] ?? 0;

            if (empty($uid)) {
                return ['code' => 0, 'msg' => $texts['messages']['invalid_login']];
            }

            // 获取提现记录
            $withdrawal = model('UserWithdrawals')->where('id', $params['withdrawal_id'])->find();
            if (!$withdrawal || $withdrawal['uid'] != $uid) {
                return ['code' => 0, 'msg' => '提现记录不存在'];
            }

            // 获取WatchPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => '支付渠道配置错误'];
            }

            // 生成代付订单号
            $transferId = 'WT' . date('YmdHis') . rand(1000, 9999);

            // 构建代付请求参数
            $transferParams = [
                'mch_id' => $config['countries']['ID']['merchant_id'],
                'mch_transferId' => $transferId,
                'transfer_amount' => $params['amount'],
                'apply_date' => date('Y-m-d H:i:s'),
                'bank_code' => $params['bank_code'],
                'receive_account' => $params['account_number'],
                'receive_name' => $params['account_name'],
                'back_url' => $this->getWithdrawalNotifyUrl(),
                'sign_type' => 'MD5'
            ];

            // 生成签名
            $transferParams['sign'] = $this->generateMD5Sign($transferParams, $config['countries']['ID']['withdrawal_key']);

            // 发送代付请求
            $apiUrl = $config['api_urls']['transfer'];
            $response = $this->sendRequest($apiUrl, $transferParams);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] === 'SUCCESS') {
                    // 更新提现记录
                    model('UserWithdrawals')->where('id', $params['withdrawal_id'])->update([
                        'trade_number' => $transferId,
                        'state' => 5, // 代付中
                        'process_time' => time(),
                        'remarks' => 'WatchPay代付处理中'
                    ]);

                    return [
                        'code' => 1,
                        'msg' => '代付订单创建成功',
                        'data' => [
                            'transfer_id' => $transferId,
                            'platform_order_no' => $result['tradeNo'] ?? '',
                            'status' => '处理中'
                        ]
                    ];
                } else {
                    return [
                        'code' => 0,
                        'msg' => $result['tradeMsg'] ?? 'WatchPay代付订单创建失败'
                    ];
                }
            } else {
                return ['code' => 0, 'msg' => 'WatchPay接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay createWithdrawal error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => '创建代付订单失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理充值回调
     */
    public function handleRechargeCallback($params)
    {
        try {
            // 验证回调签名
            if (!$this->verifyMD5Sign($params, $this->getConfig()['countries']['ID']['pay_key'] ?? '')) {
                \think\facade\Log::error('WatchPay recharge notify sign verify failed');
                return false;
            }

            // 处理充值回调
            $result = $this->handleRechargeNotify($params);
            return $result;

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay handleRechargeCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现回调
     */
    public function handleWithdrawalCallback($params)
    {
        try {
            // 验证回调签名 - 使用提现密钥
            $config = $this->getConfig();
            $withdrawalKey = $config['countries']['ID']['withdrawal_key'] ?? '';
            if (!$this->verifyMD5Sign($params, $withdrawalKey)) {
                \think\facade\Log::error('WatchPay withdrawal notify sign verify failed');
                return false;
            }

            // 处理代付回调
            $result = $this->handleWithdrawalNotify($params);
            return $result;

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay handleWithdrawalCallback error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成MD5签名
     */
    public function generateMD5Sign($params, $secretKey)
    {
        // 移除签名字段和sign_type字段，它们都不参与签名
        $signParams = $params;
        unset($signParams['sign']);
        unset($signParams['sign_type']);
        unset($signParams['signType']); // 回调时可能是signType

        // 移除空值参数
        $filteredParams = [];
        foreach ($signParams as $key => $value) {
            if ($value !== '' && $value !== null && $value !== 0) {
                $filteredParams[$key] = $value;
            }
        }

        // 按键名ASCII码排序
        ksort($filteredParams);

        // 构建签名字符串 - k=v&k=v格式
        $signStr = '';
        foreach ($filteredParams as $key => $value) {
            $signStr .= $key . '=' . $value . '&';
        }

        // 添加密钥
        $signStr .= 'key=' . $secretKey;

        // 生成MD5签名并转为小写
        $sign = strtolower(md5($signStr));

        \think\facade\Log::info('WatchPay sign debug - signStr: ' . $signStr);
        \think\facade\Log::info('WatchPay sign debug - generated sign: ' . $sign);

        return $sign;
    }

    /**
     * 验证MD5签名
     */
    public function verifyMD5Sign($params, $secretKey)
    {
        if (!isset($params['sign'])) {
            return false;
        }

        $sign = $params['sign'];
        $signParams = $params;
        // 移除所有签名相关字段，与generateMD5Sign保持一致
        unset($signParams['sign']);
        unset($signParams['sign_type']);
        unset($signParams['signType']); // 回调时可能是signType

        // 生成签名进行比较
        $expectedSign = $this->generateMD5Sign($signParams, $secretKey);

        \think\facade\Log::info('WatchPay verify sign - expected: ' . $expectedSign . ', received: ' . $sign);

        return $sign === $expectedSign;
    }

    /**
     * 发送HTTP请求
     */
    public function sendRequest($url, $params)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode == 200) {
                return $response;
            } else {
                \think\facade\Log::error("WatchPay request failed with HTTP code: {$httpCode}");
                return false;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay request error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取多语言文本
     */
    public function getTexts($lang)
    {
        $texts = [];

        // 国家名称
        if($lang=='cn') {
            $texts['countries'] = [
                'ID' => '印尼', 'IN' => '印度', 'TH' => '泰国', 'VN' => '越南', 'MY' => '马来西亚'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => '网银B2C一类', 'qris_scan_class2' => 'QRIS扫码二类', 'online_banking' => '网银支付',
                'paytm' => 'Paytm钱包', 'upi' => 'UPI支付', 'online_banking_b2c' => '网银B2C',
                'supex' => 'SUPEX扫码', 'truemoney' => 'TrueMoney钱包', 'promptpay' => 'PromptPay扫码',
                'momo' => 'MOMO钱包', 'zalopay' => 'Zalo Pay钱包', 'online_banking_direct' => '网银直连',
                'scan_payment' => '扫码支付', 'ewallet' => '电子钱包', 'online_banking_card' => '网银转卡',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm娱乐', 'paytm_scan' => 'Paytm扫码', 'upi_scan' => 'UPI扫码',
                'upi_wallet' => 'UPI钱包', 'paytm_category1' => 'Paytm跑分一类', 'paytm_native1' => 'Paytm原生一类',
                'upi_category2' => 'UPI跑分二类', 'upi_native2' => 'UPI原生二类', 'upi_category1' => 'UPI跑分一类'
            ];
            $texts['messages'] = [
                'success' => '成功', 'country_code_required' => '国家代码不能为空',
                'invalid_country' => '不支持的国家', 'param_required' => '参数不能为空',
                'invalid_login' => '用户登录信息无效', 'get_countries_failed' => '获取国家列表失败',
                'get_pay_types_failed' => '获取支付方式失败', 'order_created' => '订单创建成功'
            ];
        } elseif($lang=='en') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'ovo_wallet' => 'OVO Wallet', 'qris_scan' => 'QRIS Scan', 'online_banking' => 'Online Banking',
                'paytm' => 'Paytm Wallet', 'upi' => 'UPI Payment', 'online_banking_b2c' => 'Online Banking B2C',
                'supex' => 'SUPEX Scan', 'truemoney' => 'TrueMoney Wallet', 'promptpay' => 'PromptPay Scan',
                'momo' => 'MOMO Wallet', 'zalopay' => 'Zalo Pay Wallet', 'online_banking_direct' => 'Direct Online Banking',
                'scan_payment' => 'Scan Payment', 'ewallet' => 'E-Wallet', 'online_banking_card' => 'Online Banking Card Transfer',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Entertainment', 'paytm_scan' => 'Paytm Scan', 'upi_scan' => 'UPI Scan',
                'upi_wallet' => 'UPI Wallet', 'paytm_category1' => 'Paytm Category 1', 'paytm_native1' => 'Paytm Native 1',
                'upi_category2' => 'UPI Category 2', 'upi_native2' => 'UPI Native 2', 'upi_category1' => 'UPI Category 1'
            ];
            $texts['messages'] = [
                'success' => 'Success', 'country_code_required' => 'Country code is required',
                'invalid_country' => 'Unsupported country', 'param_required' => 'Parameter is required',
                'invalid_login' => 'Invalid user login information', 'get_countries_failed' => 'Failed to get countries list',
                'get_pay_types_failed' => 'Failed to get payment types', 'order_created' => 'Order created successfully'
            ];
        } elseif($lang=='id') {
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Internet Banking B2C Kelas 1', 'qris_scan_class2' => 'Scan QRIS Kelas 2', 'online_banking' => 'Internet Banking',
                'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
                'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
                'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
                'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
                'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Asli 1',
                'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Asli 2', 'upi_category1' => 'UPI Kategori 1'
            ];
            $texts['messages'] = [
                'success' => 'Berhasil', 'country_code_required' => 'Kode negara diperlukan',
                'invalid_country' => 'Negara tidak didukung', 'param_required' => 'Parameter diperlukan',
                'invalid_login' => 'Informasi login pengguna tidak valid', 'get_countries_failed' => 'Gagal mendapatkan daftar negara',
                'get_pay_types_failed' => 'Gagal mendapatkan jenis pembayaran', 'order_created' => 'Pesanan berhasil dibuat'
            ];
        } else {
            // 默认印尼语
            $texts['countries'] = [
                'ID' => 'Indonesia', 'IN' => 'India', 'TH' => 'Thailand', 'VN' => 'Vietnam', 'MY' => 'Malaysia'
            ];
            $texts['pay_types'] = [
                'online_banking_b2c_class1' => 'Internet Banking B2C Kelas 1', 'qris_scan_class2' => 'Scan QRIS Kelas 2', 'online_banking' => 'Internet Banking',
                'paytm' => 'Dompet Paytm', 'upi' => 'Pembayaran UPI', 'online_banking_b2c' => 'Internet Banking B2C',
                'supex' => 'Scan SUPEX', 'truemoney' => 'Dompet TrueMoney', 'promptpay' => 'Scan PromptPay',
                'momo' => 'Dompet MOMO', 'zalopay' => 'Dompet Zalo Pay', 'online_banking_direct' => 'Internet Banking Langsung',
                'scan_payment' => 'Pembayaran Scan', 'ewallet' => 'Dompet Elektronik', 'online_banking_card' => 'Transfer Kartu Internet Banking',
                // 新增支付方式
                'paytm_entertainment' => 'Paytm Hiburan', 'paytm_scan' => 'Scan Paytm', 'upi_scan' => 'Scan UPI',
                'upi_wallet' => 'Dompet UPI', 'paytm_category1' => 'Paytm Kategori 1', 'paytm_native1' => 'Paytm Asli 1',
                'upi_category2' => 'UPI Kategori 2', 'upi_native2' => 'UPI Asli 2', 'upi_category1' => 'UPI Kategori 1'
            ];
            $texts['messages'] = [
                'success' => 'Berhasil', 'country_code_required' => 'Kode negara diperlukan',
                'invalid_country' => 'Negara tidak didukung', 'param_required' => 'Parameter diperlukan',
                'invalid_login' => 'Informasi login pengguna tidak valid', 'get_countries_failed' => 'Gagal mendapatkan daftar negara',
                'get_pay_types_failed' => 'Gagal mendapatkan jenis pembayaran', 'order_created' => 'Pesanan berhasil dibuat'
            ];
        }

        return $texts;
    }

    /**
     * 获取支付类型分类
     */
    public function getPayTypeCategory($typeCode)
    {
        $categories = [
            '220' => 'online',  // 网银B2C一类
            '223' => 'scan',    // QRIS扫码二类
            '104' => 'wallet',  // Paytm娱乐
            '131' => 'wallet',  // Paytm跑分一类
            '101' => 'wallet',  // Paytm原生一类
            '105' => 'online',  // UPI娱乐
            '122' => 'online',  // UPI跑分二类
            '152' => 'online',  // UPI原生二类
            '132' => 'online',  // UPI跑分一类
        ];

        return $categories[$typeCode] ?? 'online';
    }

    /**
     * 获取国家旗帜
     */
    public function getCountryFlag($countryCode)
    {
        $flags = [
            'ID' => '🇮🇩',
            'IN' => '🇮🇳',
            'TH' => '🇹🇭',
            'VN' => '🇻🇳',
            'MY' => '🇲🇾',
            'BR' => '🇧🇷'
        ];

        return $flags[$countryCode] ?? '🌍';
    }

    /**
     * 获取回调URL
     */
    private function getNotifyUrl($rechargeId)
    {
        // 优先从配置文件获取统一回调URL
        $notifyUrl = $this->getNotifyUrlFromConfig();

        if ($notifyUrl) {
            return $notifyUrl;
        }

        // 兜底方案：从全局配置获取默认域名生成统一充值回调地址
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (file_exists($configPath)) {
                $config = include $configPath;
                $defaultDomain = $config['global']['default_notify_domain'] ?? 'http://localhost';
                return rtrim($defaultDomain, '/') . '/api/transaction/unifiedCallback';
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用最终兜底方案
        }

        // 最终兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedCallback';
    }

    /**
     * 从配置文件获取回调URL
     */
    private function getNotifyUrlFromConfig()
    {
        try {
            // 直接读取配置文件
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (!file_exists($configPath)) {
                return null;
            }

            $paymentConfig = include($configPath);

            // 直接使用全局统一充值回调地址
            return $paymentConfig['global']['unified_recharge_callback_url'] ?? null;

        } catch (\Exception $e) {
            \think\facade\Log::error("Failed to get notify URL from config: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取代付回调URL
     */
    private function getWithdrawalNotifyUrl()
    {
        try {
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $config = include $configPath;
                $notifyUrl = $config['global']['unified_withdrawal_callback_url'] ?? null;
                if ($notifyUrl) {
                    return $notifyUrl;
                }
            }
        } catch (\Exception $e) {
            // 配置文件读取失败，使用兜底方案
        }

        // 兜底方案：自动检测当前服务器信息
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';

        return $protocol . '://' . $host . '/api/transaction/unifiedWithdrawalCallback';
    }

    /**
     * 获取支付方式的多语言名称
     */
    private function getLocalizedPayTypeName($typeCode, $texts)
    {
        // 支付方式代码到多语言键的映射
        $payTypeMapping = [
            '220' => 'online_banking_b2c_class1',
            '223' => 'qris_scan_class2',
            '101' => 'paytm_native1',
            '104' => 'paytm_entertainment',
            '105' => 'upi_scan',
            '122' => 'upi_category2',
            '131' => 'paytm_category1',
            '132' => 'upi_category1',
            '152' => 'upi_native2'
        ];

        $langKey = $payTypeMapping[$typeCode] ?? null;
        return $langKey ? ($texts['pay_types'][$langKey] ?? null) : null;
    }

    /**
     * 处理充值回调
     */
    private function handleRechargeNotify($params)
    {
        try {
            $orderNo = $params['mchOrderNo'] ?? '';
            $tradeResult = $params['tradeResult'] ?? '';
            $amount = $params['amount'] ?? 0;

            if (empty($orderNo)) {
                \think\facade\Log::error('WatchPay recharge notify: missing order number');
                return false;
            }

            // 查找充值记录
            $recharge = model('UserRecharge')->where('order_number', $orderNo)->find();
            if (!$recharge) {
                \think\facade\Log::error("WatchPay recharge notify: order not found - {$orderNo}");
                return false;
            }

            // 检查订单状态
            if ($recharge['state'] == 1) {
                \think\facade\Log::info("WatchPay recharge notify: order already processed - {$orderNo}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($recharge['money'] - $notifyAmount) > 0.01) {
                \think\facade\Log::error("WatchPay recharge notify: amount mismatch - order: {$recharge['money']}, notify: {$notifyAmount}");
                return false;
            }

            // 处理支付结果
            if ($tradeResult == '1') {
                // 支付成功
                return $this->processRechargeSuccess($recharge);
            } else {
                // 支付失败
                model('UserRecharge')->where('id', $recharge['id'])->update([
                    'state' => 2,
                    'dispose_time' => time(),
                    'remarks' => 'WatchPay支付失败，交易结果：' . $tradeResult
                ]);
                return true;
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay handleRechargeNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理代付回调
     */
    private function handleWithdrawalNotify($params)
    {
        try {
            $transferId = $params['merTransferId'] ?? '';
            $tradeResult = $params['tradeResult'] ?? '';
            $amount = $params['transferAmount'] ?? 0;

            if (empty($transferId)) {
                \think\facade\Log::error('WatchPay withdrawal notify: missing transfer ID');
                return false;
            }

            // 查找提现记录 - merTransferId对应order_number字段
            $withdrawal = model('UserWithdrawals')->where('order_number', $transferId)->find();
            if (!$withdrawal) {
                \think\facade\Log::error("WatchPay withdrawal notify: order not found - {$transferId}");
                return false;
            }

            // 检查订单状态
            if ($withdrawal['state'] == 1) {
                \think\facade\Log::info("WatchPay withdrawal notify: order already processed - {$transferId}");
                return true;
            }

            // 验证金额
            $notifyAmount = floatval($amount);
            if (abs($withdrawal['price'] - $notifyAmount) > 0.01) {
                \think\facade\Log::error("WatchPay withdrawal notify: amount mismatch - order: {$withdrawal['price']}, notify: {$notifyAmount}");
                return false;
            }

            // 处理代付结果
            if ($tradeResult == '1') {
                // 代付成功
                return $this->processWithdrawalSuccess($withdrawal);
            } else {
                // 代付失败
                return $this->processWithdrawalFailed($withdrawal, '代付失败，状态：' . $tradeResult);
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay handleWithdrawalNotify error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理充值成功
     */
    private function processRechargeSuccess($recharge)
    {
        try {
            // 开启事务
            model('UserRecharge')->startTrans();

            // 更新充值记录状态
            model('UserRecharge')->where('id', $recharge['id'])->update([
                'state' => 1,
                'dispose_time' => time(),
                'remarks' => 'WatchPay支付成功'
            ]);

            // 更新用户余额
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('balance', $recharge['daozhang_money']);
            model('UserTotal')->where('uid', $recharge['uid'])->setInc('total_balance', $recharge['daozhang_money']);

            // 获取更新后的余额
            $newBalance = model('UserTotal')->where('uid', $recharge['uid'])->value('balance');

            // 获取用户信息
            $user = model('Users')->where('id', $recharge['uid'])->find();

            // 添加资金流水记录
            $tradeData = [
                'uid' => $recharge['uid'],
                'username' => $user['username'] ?? '',
                'vip_level' => $user['vip_level'] ?? 0,
                'user_type' => $user['user_type'] ?? 2,
                'source_uid' => $recharge['uid'],
                'source_username' => $user['username'] ?? '',
                'order_number' => $recharge['order_number'],
                'trade_number' => $recharge['order_number'],
                'trade_time' => time(),
                'trade_type' => 1, // 充值
                'trade_amount' => $recharge['daozhang_money'],
                'trade_before_balance' => $newBalance - $recharge['daozhang_money'],
                'account_balance' => $newBalance,
                'account_total_balance' => $newBalance,
                'remarks' => 'WatchPay充值',
                'remarks_en' => 'WatchPay Recharge',
                'remarks_id' => 'Top Up WatchPay',
                'remarks_ft' => 'WatchPay充值',
                'remarks_yd' => 'WatchPay रिचार्ज',
                'remarks_vi' => 'Nạp tiền WatchPay',
                'remarks_es' => 'Recarga WatchPay',
                'remarks_ja' => 'WatchPayチャージ',
                'remarks_th' => 'เติมเงิน WatchPay',
                'remarks_ma' => 'Top Up WatchPay',
                'remarks_pt' => 'Recarga WatchPay',
                'state' => 1,
                'isadmin' => 2,
                'types' => 1 // 用户类型
            ];

            model('UserTransaction')->insert($tradeData);

            // 提交事务
            model('UserRecharge')->commit();

            \think\facade\Log::info("WatchPay recharge success: {$recharge['order_number']}, amount: {$recharge['daozhang_money']}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserRecharge')->rollback();
            \think\facade\Log::error("WatchPay recharge success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现成功
     */
    private function processWithdrawalSuccess($withdrawal)
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 1, // 成功
                'set_time' => time(),
                'remarks' => 'WatchPay代付成功'
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            \think\facade\Log::info("WatchPay withdrawal success: {$withdrawal['trade_number']}, amount: {$withdrawal['price']}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            \think\facade\Log::error("WatchPay withdrawal success transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理提现失败
     */
    private function processWithdrawalFailed($withdrawal, $reason = '代付失败')
    {
        try {
            // 开启事务
            model('UserWithdrawals')->startTrans();

            // 更新提现记录状态
            model('UserWithdrawals')->where('id', $withdrawal['id'])->update([
                'state' => 2, // 失败
                'set_time' => time(),
                'remarks' => $reason
            ]);

            // 退还用户余额
            model('UserTotal')->where('uid', $withdrawal['uid'])->setInc('balance', $withdrawal['price'] + $withdrawal['fee']);

            // 添加资金流水
            $userTotal = model('UserTotal')->where('uid', $withdrawal['uid'])->find();
            model('UserTransaction')->insert([
                'uid' => $withdrawal['uid'],
                'type' => 1, // 退款
                'money' => $withdrawal['price'] + $withdrawal['fee'],
                'balance' => $userTotal['balance'],
                'remarks' => '提现失败退款：' . $withdrawal['trade_number'],
                'time' => time()
            ]);

            // 提交事务
            model('UserWithdrawals')->commit();

            \think\facade\Log::info("WatchPay withdrawal failed: {$withdrawal['trade_number']}, reason: {$reason}");
            return true;

        } catch (\Exception $e) {
            // 回滚事务
            model('UserWithdrawals')->rollback();
            \think\facade\Log::error("WatchPay withdrawal failed transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 管理后台直接代付方法(绕过用户验证)
     */
    public function processWithdrawalDirect($withdrawalData, $channel)
    {
        try {
            // 获取WatchPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => 'WatchPay配置不完整'];
            }

            // 构建WatchPay代付请求参数
            $transferParams = [
                'mch_id' => $config['countries']['ID']['merchant_id'],
                'mch_transferId' => $withdrawalData['order_number'],
                'transfer_amount' => (string)intval(floatval($withdrawalData['price'])),
                'apply_date' => date('Y-m-d H:i:s'),
                'bank_code' => $this->getBankCodeFromBankId($withdrawalData['bank_id']),
                'receive_name' => $withdrawalData['card_name'],
                'receive_account' => $withdrawalData['card_number'],
                'back_url' => $this->getWithdrawalNotifyUrl(),
                'sign_type' => 'MD5'
            ];

            // 生成签名
            $transferParams['sign'] = $this->generateMD5Sign($transferParams, $config['countries']['ID']['withdrawal_key']);

            // 发送代付请求
            $transferUrl = $config['api_urls']['transfer'];
            $response = $this->sendRequest($transferUrl, $transferParams);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] == 'SUCCESS') {
                    \think\facade\Log::info('WatchPay withdrawal direct success: ' . $response);

                    // 处理同步回调状态
                    $tradeResult = $result['tradeResult'] ?? '';
                    if ($tradeResult === '1') {
                        // 同步返回转账成功，立即处理成功状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $this->processWithdrawalSuccess($withdrawal);
                            \think\facade\Log::info("WatchPay withdrawal sync success processed: {$withdrawalData['order_number']}");
                            return ['code' => 1, 'msg' => '代付成功', 'data' => $result];
                        }
                    } elseif ($tradeResult === '2' || $tradeResult === '3') {
                        // 同步返回转账失败或拒绝，立即处理失败状态
                        $withdrawal = model('UserWithdrawals')->where('order_number', $withdrawalData['order_number'])->find();
                        if ($withdrawal) {
                            $failureReason = $tradeResult === '2' ? '转账失败' : '转账拒绝';
                            $this->processWithdrawalFailed($withdrawal, 'WatchPay代付失败：' . $failureReason);
                            \think\facade\Log::info("WatchPay withdrawal sync failed processed: {$withdrawalData['order_number']}, reason: {$failureReason}");
                            return ['code' => 0, 'msg' => 'WatchPay代付失败：' . $failureReason];
                        }
                    } else {
                        // 其他状态（0=申请成功，4=处理中），等待异步回调
                        \think\facade\Log::info("WatchPay withdrawal sync status {$tradeResult}, waiting for async callback: {$withdrawalData['order_number']}");
                    }

                    return ['code' => 1, 'msg' => '代付请求已提交', 'data' => $result];
                } else {
                    $errorMsg = $result['errorMsg'] ?? '代付请求失败';
                    \think\facade\Log::error('WatchPay withdrawal direct failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg];
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay processWithdrawalDirect error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay代付异常：' . $e->getMessage()];
        }
    }

    /**
     * 根据银行ID获取银行编码
     */
    private function getBankCodeFromBankId($bankId, $channel = 'watchpay')
    {
        try {
            // 从配置文件读取银行ID映射
            $configPath = dirname(dirname(dirname(__DIR__))) . '/config/payment_config.php';
            if (!file_exists($configPath)) {
                $configPath = $_SERVER['DOCUMENT_ROOT'] . '/config/payment_config.php';
            }

            if (file_exists($configPath)) {
                $paymentConfig = include($configPath);
                
                // 查找bank_code_mapping配置
                $bankCodeMapping = $paymentConfig['bank_code_mapping'] ?? [];
                
                // 遍历映射找到对应bank_id的银行
                foreach ($bankCodeMapping as $bankName => $bankInfo) {
                    if (isset($bankInfo['bank_id']) && $bankInfo['bank_id'] == $bankId) {
                        if ($channel === 'jayapay') {
                            $code = $bankInfo['jayapay'] ?? 'BCA';
                        } else {
                            $code = $bankInfo['watchpay'] ?? 'BCA';
                        }
                        
                        \think\facade\Log::info("Found bank code for bank_id {$bankId}: {$bankName} -> {$code}");
                        return $code;
                    }
                }
                
                \think\facade\Log::warning("Bank ID {$bankId} not found in config, using default BCA");
            }
        } catch (\Exception $e) {
            \think\facade\Log::error('getBankCodeFromBankId error: ' . $e->getMessage());
        }

        // 默认返回值
        return $channel === 'jayapay' ? '014' : 'BCA';
    }



    /**
     * 创建WatchPay充值订单（直接调用API）
     */
    private function createWatchPayRechargeOrder($orderData)
    {
        try {
            // 获取WatchPay配置
            $config = $this->getConfig();
            if (!$config) {
                return ['code' => 0, 'msg' => 'WatchPay配置不完整'];
            }

            // 构建WatchPay充值请求参数
            $params = [
                'version' => '1.0',
                'mch_id' => $config['countries']['ID']['merchant_id'],
                'mch_order_no' => $orderData['order_no'],
                'pay_type' => $orderData['pay_type'],
                'trade_amount' => $orderData['amount'],
                'notify_url' => $orderData['notify_url'],
                'order_date' => date('Y-m-d H:i:s'),
                'goods_name' => '在线充值',
                'sign_type' => 'MD5'
            ];

            // 如果有银行编码，添加到参数中
            if (!empty($orderData['bank_code'])) {
                $params['bank_code'] = $orderData['bank_code'];
            }

            // 生成签名
            $params['sign'] = $this->generateMD5Sign($params, $config['countries']['ID']['pay_key']);

            // 发送WatchPay充值请求
            $rechargeUrl = $config['api_urls']['pay'];
            $response = $this->sendRequest($rechargeUrl, $params);

            if ($response) {
                $result = json_decode($response, true);
                if ($result && isset($result['respCode']) && $result['respCode'] === 'SUCCESS') {
                    \think\facade\Log::info('WatchPay recharge order created: ' . $response);
                    return [
                        'code' => 1, 
                        'msg' => '充值订单创建成功', 
                        'data' => [
                            'pay_url' => $result['payInfo'] ?? '',
                            'platform_order_no' => $result['orderNo'] ?? ''
                        ]
                    ];
                } else {
                    $errorMsg = $result['respMsg'] ?? '充值订单创建失败';
                    \think\facade\Log::error('WatchPay recharge order failed: ' . $response);
                    return ['code' => 0, 'msg' => $errorMsg];
                }
            } else {
                return ['code' => 0, 'msg' => '接口请求失败'];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('WatchPay createWatchPayRechargeOrder error: ' . $e->getMessage());
            return ['code' => 0, 'msg' => 'WatchPay充值异常：' . $e->getMessage()];
        }
    }
}