<?php /*a:1:{s:64:"/var/www/html/application/manage/view/bank/recharge_dispose.html";i:**********;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>充值处理</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/resource/layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/resource/css/mylay.css">
</head>
<body>
    <div style="padding: 20px; background-color: #F2F2F2;">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <form class="layui-form" action="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">订单编号</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="order_number" value="<?php echo htmlentities($data['order_number']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">充值金额</label>
                                <div class="layui-input-inline">
                                    <input readonly="readonly" type="text" name="money" value="<?php echo htmlentities($data['money']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" >
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">到账金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="daozhang_money" value="<?php echo htmlentities($data['daozhang_money']); ?>"  autocomplete="off" placeholder="" class="layui-input" >
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            
                            
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款账号</label>
                                <div class="layui-input-block">
                                    <input type="text" name="" value="<?php echo htmlentities($data['name']); ?>" value="" autocomplete="off" placeholder="" class="layui-input" readonly>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
							<div class="layui-form-item">
                                <label class="layui-form-label">转账截图</label>
                                <div class="layui-input-block"> <?php foreach($data['screenshots'] as $key=>$value): ?> <img src="<?php echo isset($value) ? htmlentities($value) : ''; ?>" style="max-width: 150px"> <?php endforeach; ?> </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理结果</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="state" value="1" title="成功"<?php if($data['state'] == 1): ?> checked<?php endif; ?>>
                                    <input type="radio" name="state" value="2" title="失败"<?php if($data['state'] == 2): ?> checked<?php endif; ?>>
                                    <input type="radio" name="state" value="3" title="审核中"<?php if($data['state'] == 3): ?> checked<?php endif; ?>>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">处理说明</label>
                                <div class="layui-input-block">
                                    <textarea name="remarks" placeholder="处理说明" class="layui-textarea"></textarea>
                                </div>
                                <div class="layui-form-mid layui-word-aux"></div>
                            </div>
                            <div class="layui-form-item" style="margin-top: 40px;text-align: center;">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="rechargedispose">立即提交</button>
                                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="/resource/layuiadmin/layui/layui.js"></script>
<script src="/resource/js/manage/init_date.js"></script>
<script src="/resource/js/manage/bank.js"></script>
</body>
</html>